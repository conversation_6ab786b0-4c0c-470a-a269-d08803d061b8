# 活动类型管理功能说明

## 功能概述
活动类型管理用于创建和管理活动类型，对应用户端类型切换tab。

## 页面路径
- 路由路径：`/act-mange/act-type-list`
- 页面组件：`src/views/act-manage/actTypeList.vue`
- 弹窗组件：`src/views/act-manage/components/activityTypeDialog.vue`

## 功能特性

### 1. 查询功能
- **类型ID（精确搜索）**：可输入系统自动生成活动类型编码查询
- **类型名称（支持模糊搜索）**：可输入创建活动类型时输入的类型名称查询
- **状态**：默认全部，可选开启/关闭活动
- **创建时间**：按创建活动类型的时间段查询

### 2. 操作功能
- **新增**：创建新的活动类型
- **详情**：查看活动类型详细信息（只读模式）
- **修改**：编辑活动类型信息
- **删除**：删除状态为关闭的活动类型（逻辑删除）

### 3. 列表字段
- **活动类型ID**：系统自动生成，具有唯一性
- **活动类型名称**：对应活动类型名称
- **排序**：对应活动类型的排序
- **创建时间**：创建该活动类型的时间，格式：yyyy-mm-dd HH:mm:ss
- **更新时间**：活动类型信息最近一次更新的时间
- **更新者**：最近修改活动类型信息的系统账号
- **状态**：开启/关闭

## 表单字段说明

### 新增/修改表单
1. **活动类型名称（必填）**：文本框，长度上限10个字（用户端展示）
2. **用户端排序（必填）**：用户端活动类型排序按此字段展示。从0开始，默认按列表最大排序自增
3. **状态（必选）**：开启/关闭（开启状态表示该活动类型以及活动均在用户端展示）
4. **icon（必填）**：支持上传1张1:1尺寸的图，支持常规的图片格式（jpg、JPEG、png、gif、svg），大小不超过500k
5. **备注（非必填）**：多行文本框，长度上限200个字（仅后管可见）

## API接口

### 获取活动类型列表
- **接口地址**：`/activity-manager-api/activityType/getActivityType`
- **请求方式**：POST
- **参数**：typeId, typeTitle, actTypeStatus, createTimeStart, createTimeEnd, delFlag

### 新增活动类型
- **接口地址**：`/activity-manager-api/activityType/addType`
- **请求方式**：POST
- **参数**：sort, typeTitle, actTypeStatus, remark, typeCoverImg

### 修改活动类型
- **接口地址**：`/activity-manager-api/activityType/modifyType`
- **请求方式**：POST
- **参数**：typeId, typeTitle, sort, actTypeStatus, remark, typeCoverImg

### 获取活动类型详情
- **接口地址**：`/activity-manager-api/activityType/getTypeDetail`
- **请求方式**：POST
- **参数**：typeId

### 删除活动类型
- **接口地址**：`/activity-manager-api/activityType/modifyType`
- **请求方式**：POST
- **说明**：逻辑删除，设置delFlag为'1'

## 注意事项
1. 删除活动类型后，活动管理中已新增的原属于该活动类型的活动数据保留，但所属活动类型为空
2. 订单管理中该活动类型的历史订单及统计报表数据保留
3. 只能删除状态为关闭的活动类型
4. 用户端除"全部"外，其余分类从左到右按排序的升序排列；排序相同时，再按创建时间倒序排
